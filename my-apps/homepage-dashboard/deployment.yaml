apiVersion: apps/v1
kind: Deployment
metadata:
  name: homepage-dashboard
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: homepage-dashboard
  template:
    metadata:
      labels:
        app: homepage-dashboard
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: homepage-dashboard
        image: ghcr.io/benphelps/homepage:latest
        ports:
        - containerPort: 3000
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: [ "ALL" ]
        volumeMounts:
        - name: config
          mountPath: /app/config
      volumes:
      - name: config
        persistentVolumeClaim:
          claimName: homepage-config
