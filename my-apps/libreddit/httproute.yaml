apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: libreddit-route
  namespace: libreddit
spec:
  parentRefs:
    - name: gateway-external
      namespace: gateway
      sectionName: http
    - name: gateway-external
      namespace: gateway
      sectionName: https
  hostnames:
    - "libreddit.mvt.com.ar"
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: libreddit
          port: 8080
