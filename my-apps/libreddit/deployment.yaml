apiVersion: apps/v1
kind: Deployment
metadata:
  name: libreddit
  namespace: libreddit
  labels:
    app.kubernetes.io/name: libreddit
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: libreddit
  replicas: 1
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: libreddit
    spec:
      nodeSelector:
        kubernetes.io/hostname: vanillax-nuc
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: libreddit
          image: quay.io/redlib/redlib:latest
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: libreddit-env
          ports:
            - name: http
              containerPort: 8080
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop: ["ALL"]
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 2000m
              memory: 1Gi
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
