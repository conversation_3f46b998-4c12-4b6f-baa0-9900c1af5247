apiVersion: v1
kind: ConfigMap
metadata:
  name: libreddit-env
  namespace: libreddit
data:
  # Instance settings
  REDLIB_SFW_ONLY: "off"
  REDLIB_ROBOTS_DISABLE_INDEXING: "off"
  REDLIB_PORT: "8080"
  REDLIB_ENABLE_RSS: "off"
  
  # Default user settings
  REDLIB_DEFAULT_THEME: "dark"
  REDLIB_DEFAULT_FRONT_PAGE: "popular"
  REDLIB_DEFAULT_LAYOUT: "card"
  REDLIB_DEFAULT_WIDE: "on"
  REDLIB_DEFAULT_POST_SORT: "hot"
  REDLIB_DEFAULT_COMMENT_SORT: "confidence"
  REDLIB_DEFAULT_BLUR_SPOILER: "on"
  REDLIB_DEFAULT_SHOW_NSFW: "on"
  REDLIB_DEFAULT_BLUR_NSFW: "off"
  REDLIB_DEFAULT_USE_HLS: "on"
  REDLIB_DEFAULT_HIDE_HLS_NOTIFICATION: "on"
  REDLIB_DEFAULT_AUTOPLAY_VIDEOS: "off"
  REDLIB_DEFAULT_HIDE_AWARDS: "on"
  REDLIB_DEFAULT_DISABLE_VISIT_REDDIT_CONFIRMATION: "on"
  REDLIB_DEFAULT_HIDE_SCORE: "off"
  REDLIB_DEFAULT_HIDE_SIDEBAR_AND_SUMMARY: "off"
  REDLIB_DEFAULT_FIXED_NAVBAR: "on"
  
  # System settings
  TZ: "America/Detroit"