apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: kube-system
labels:
  - pairs:
      app.kubernetes.io/name: headlamp
      app.kubernetes.io/managed-by: argocd
resources:
  - namespace.yaml
  - httproute.yaml
  - serviceaccount.yaml
  - token-secret.yaml
helmCharts:
  - name: headlamp
    repo: https://kubernetes-sigs.github.io/headlamp/
    version: "0.34.0"
    releaseName: headlamp
    namespace: kube-system
    includeCRDs: true
    valuesFile: values.yaml
