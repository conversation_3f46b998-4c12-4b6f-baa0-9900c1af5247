apiVersion: apps/v1
kind: Deployment
metadata:
  name: searxng
  namespace: searxng
  labels:
    app.kubernetes.io/name: searxng
spec:
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: searxng
  strategy:
    type: RollingUpdate
  revisionHistoryLimit: 1
  template:
    metadata:
      namespace: searxng
      labels:
        app.kubernetes.io/name: searxng
    spec:
      restartPolicy: Always
      containers:
        - name: searxng
          image: searxng/searxng:2024.4.29-e45a7cc06
          resources:
            requests:
              memory: 100M
              cpu: 50m
            limits:
              memory: 3000M
          envFrom:
            - configMapRef:
                name: searxng
            - secretRef:
                name: searxng
          ports:
            - containerPort: 8080
              name: http
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 60
          volumeMounts:
            - name: searxng-settings
              mountPath: /etc/searxng/settings.yml
              subPath: settings.yaml
            - name: searxng-limiter
              mountPath: /etc/searxng/limiter.toml
              subPath: limiter.toml
      volumes:
        - name: searxng-settings
          configMap:
            name: searxng-config-settings
            items:
              - key: settings.yaml
                path: settings.yaml
        - name: searxng-limiter
          configMap:
            name: searxng-config-settings
            items:
              - key: limiter.toml
                path: limiter.toml
