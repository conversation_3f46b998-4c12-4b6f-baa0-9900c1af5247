apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: searxng
  namespace: searxng
spec:
  parentRefs:
    - name: gateway-external # Must match your Gateway's metadata.name
      namespace: gateway
      sectionName: https # Must match your Gateway's listeners[].name
  hostnames:
    - "search.mvt.com.ar"
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: searxng
          port: 8080
