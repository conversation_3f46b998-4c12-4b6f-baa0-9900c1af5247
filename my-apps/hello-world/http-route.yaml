apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: hello-world
  namespace: hello-world
spec:
  parentRefs:
    - name: gateway-internal    # Must match your Gateway's metadata.name
      namespace: gateway
      sectionName: https       # Must match your Gateway's listeners[].name
  hostnames:
  - "hello-world.mvt.com.ar"
  rules:
  - matches:
    - path:
        type: PathPrefix 
        value: /
    backendRefs:
    - name: hello-world
      port: 8080
