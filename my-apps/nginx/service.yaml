# nginx-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx
  annotations:
    # Request a specific IP from the Cilium IPAM pool
    #this is only required if you want to request a specific IP from the Cilium IPAM pool
    #ie you want a static IP for the service
    #Optional: if you want to use L2 announcements for the LoadBalancer
    lbipam.cilium.io/ips: "**************"
spec:
  type: LoadBalancer
  # Use L2 announcements for the LoadBalancer
  #See comment above about lbipam.cilium.io/ips
  #OPTIONAL: if you want to use L2 announcements for the LoadBalancer
  loadBalancerClass: io.cilium/l2-announcer
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
