apiVersion: v1
kind: Service
metadata:
  name: proxitok-web
  namespace: proxitok
spec:
  selector:
    app: proxitok-web
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: proxitok-redis
  namespace: proxitok
spec:
  selector:
    app: proxitok-redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: proxitok-chromedriver
  namespace: proxitok
spec:
  selector:
    app: proxitok-chromedriver
  ports:
  - port: 9515
    targetPort: 9515
  type: ClusterIP