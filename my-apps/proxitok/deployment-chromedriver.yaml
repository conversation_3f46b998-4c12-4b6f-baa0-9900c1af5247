apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxitok-chromedriver
  namespace: proxitok
spec:
  replicas: 1
  selector:
    matchLabels:
      app: proxitok-chromedriver
  template:
    metadata:
      labels:
        app: proxitok-chromedriver
    spec:
      containers:
      - name: chromedriver
        image: zenika/alpine-chrome:with-chromedriver
        ports:
        - containerPort: 9515
        args:
          - --no-sandbox
          - --headless
          - --disable-dev-shm-usage
          - --disable-gpu
          - --remote-debugging-port=9222
        env:
        - name: CHROME_OPTS
          value: "--disable-dev-shm-usage --no-sandbox --headless --disable-gpu --disable-software-rasterizer --remote-debugging-port=9222"
        - name: CHROMEDRIVER_OPTS
          value: "--verbose --whitelisted-ips=''"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "4000m"
        volumeMounts:
        - name: dshm
          mountPath: /dev/shm
        securityContext:
          capabilities:
            drop:
            - ALL
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
        readinessProbe:
          tcpSocket:
            port: 9515
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          tcpSocket:
            port: 9515
          initialDelaySeconds: 15
          periodSeconds: 15
      volumes:
      - name: dshm
        emptyDir:
          medium: Memory
          sizeLimit: 3Gi