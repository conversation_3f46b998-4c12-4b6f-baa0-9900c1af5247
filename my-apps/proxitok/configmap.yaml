apiVersion: v1
kind: ConfigMap
metadata:
  name: proxitok-config
  namespace: proxitok
data:
  APP_URL: "https://proxitok.mvt.com.ar"
  LATTE_CACHE: "/cache"
  API_CACHE: "redis"
  REDIS_HOST: "proxitok-redis.proxitok.svc.cluster.local"
  REDIS_PORT: "6379"
  API_CHROMEDRIVER: "http://proxitok-chromedriver.proxitok.svc.cluster.local:9515"
    # Add these for better stability
  CHROME_TIMEOUT: "30"
  CHROME_RETRIES: "3"
  API_REGION: "US"  # or try other regions like UK, DE
  API_LANGUAGE: "en"
  API_USER_AGENT: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"