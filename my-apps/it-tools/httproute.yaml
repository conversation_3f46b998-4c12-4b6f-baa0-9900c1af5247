apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: it-tools-route
  namespace: it-tools
spec:
  parentRefs:
    - name: gateway-external
      namespace: gateway
      sectionName: https
  hostnames:
    - "it-tools.mvt.com.ar"
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: it-tools
          port: 8080
          weight: 1
