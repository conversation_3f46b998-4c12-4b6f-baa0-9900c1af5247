apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: prometheus
  namespace: kube-prometheus-stack
spec:
  parentRefs:
  - name: gateway-internal # Must match your Gateway's metadata.name
    namespace: gateway
    sectionName: https # Must match your Gateway's listeners[].name
  hostnames:
  - "prometheus.mvt.com.ar"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: prometheus-operated
      port: 9090
