apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: grafana
  namespace: kube-prometheus-stack
spec:
  parentRefs:
  - name: gateway-internal # Must match your Gateway's metadata.name
    namespace: gateway
    sectionName: https # Must match your Gateway's listeners[].name
  hostnames:
  - "grafana.mvt.com.ar"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: prometheus-grafana
      port: 80
