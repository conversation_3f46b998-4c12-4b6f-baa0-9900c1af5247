apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: infrastructure-components
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  generators:
  - git:
      repoURL: https://github.com/axelmvt/k3s-argocd-starter
      revision: HEAD
      directories:
      - path: infrastructure/networking/*
      - path: infrastructure/storage/*
      - path: infrastructure/controllers/*
  template:
    metadata:
      name: '{{path.basename}}'
      labels:
        type: infrastructure
    spec:
      project: infrastructure
      source:
        plugin:
          name: kustomize-build-with-helm
        repoURL: https://github.com/axelmvt/k3s-argocd-starter
        targetRevision: HEAD
        path: '{{path}}'
      destination:
        server: https://kubernetes.default.svc
        namespace: '{{path.basename}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        retry:
          limit: 5
          backoff:
            duration: 5s
            factor: 2
            maxDuration: 3m
        syncOptions:
        - CreateNamespace=true
        - ServerSideApply=true
        - RespectIgnoreDifferences=true
        - ApplyOutOfSyncOnly=true
        - Replace=false
        - IgnoreMissingTemplate=true
