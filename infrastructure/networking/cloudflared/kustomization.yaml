apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: cloudflared-daemonset
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
namespace: cloudflared
resources:
  - ns.yaml
  - deployment.yaml
generatorOptions:
  disableNameSuffixHash: true
configMapGenerator:
  - name: config
    files:
      - config.yaml
    options:
      annotations:
        argocd.argoproj.io/sync-wave: "-2"

# The secret will be created manually via kubectl
# ... existing code ...
