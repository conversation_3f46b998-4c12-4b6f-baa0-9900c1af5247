tunnel: k3s
credentials-file: /etc/cloudflared/credentials/credentials.json
metrics: 0.0.0.0:2000
no-autoupdate: true
# Enhanced logging configuration
loglevel: debug # Increased log level for more details
transport-loglevel: debug
ingress:
  - hostname: hello.mvt.com.ar
    service: hello_world
    originRequest:
      # Add header logging with log-headers flag
      logHeaders: true
      headers:
        - name: CF-Connecting-IP
        - name: X-Real-IP
        - name: CF-IPCountry
        - name: CF-Ray
        - name: CF-Visitor
        - name: CF-Device-Type
        - name: CF-IPCITY
        - name: CF-IPCONTINENT
        - name: CF-IPLATITUDE
        - name: CF-IPLONGITUDE
        - name: CF-IPREGION
        - name: X-Forwarded-For
        - name: Accept-Language
        - name: User-Agent
  - hostname: "*.mvt.com.ar"
    service: https://cilium-gateway-gateway-external.gateway.svc.cluster.local:443
    originRequest:
      noTLSVerify: true
      originServerName: "*.mvt.com.ar"
      logHeaders: true
      headers:
        - name: CF-Connecting-IP
        - name: X-Real-IP
        - name: CF-IPCountry
        - name: CF-Ray
        - name: CF-Visitor
        - name: CF-Device-Type
        - name: CF-IPCITY
        - name: CF-IPCONTINENT
        - name: CF-IPLATITUDE
        - name: CF-IPLONGITUDE
        - name: CF-IPREGION
        - name: X-Forwarded-For
        - name: Accept-Language
        - name: User-Agent
        - name: True-Client-IP
        - name: CF-IPCountry
        - name: CF-RAY
        - name: CF-Worker
        - name: CF-Client-Bot
        - name: CF-Bot-Score
        - name: CF-Challenge
        - name: CF-Bot-Management-Tag
  - hostname: mvt.com.ar
    service: https://cilium-gateway-gateway-external.gateway.svc.cluster.local:443
    originRequest:
      noTLSVerify: true
      originServerName: mvt.com.ar
      logHeaders: true
      headers:
        - name: CF-Connecting-IP
        - name: X-Real-IP
        - name: CF-IPCountry
        - name: CF-Ray
        - name: CF-Visitor
        - name: CF-Device-Type
        - name: CF-IPCITY
        - name: CF-IPCONTINENT
        - name: CF-IPLATITUDE
        - name: CF-IPLONGITUDE
        - name: CF-IPREGION
        - name: X-Forwarded-For
        - name: Accept-Language
        - name: User-Agent
  - service: http_status:404
