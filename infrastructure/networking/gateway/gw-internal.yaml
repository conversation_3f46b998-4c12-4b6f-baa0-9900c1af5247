apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gateway-internal
  namespace: gateway
spec:
  addresses:
  - type: IPAddress
    value: *************
  gatewayClassName: cilium
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "*.mvt.com.ar"
      allowedRoutes:
        namespaces:
          from: All
    - name: https
      protocol: HTTPS
      port: 443
      hostname: "*.mvt.com.ar"
      tls:
        certificateRefs:
          - name: cert-vanillax
            kind: Secret
            group: "" # required
      allowedRoutes:
        namespaces:
          from: All
