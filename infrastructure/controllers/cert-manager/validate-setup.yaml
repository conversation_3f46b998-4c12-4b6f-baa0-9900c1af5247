apiVersion: batch/v1
kind: Job
metadata:
  name: cert-manager-validation
  namespace: cert-manager
  annotations:
    argocd.argoproj.io/sync-wave: "3" # Run after ClusterIssuer is ready
    argocd.argoproj.io/hook: PostSync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
        - name: validate-cert-manager
          image: bitnami/kubectl:latest
          command:
            - /bin/sh
            - -c
            - |
              echo "=== Validating cert-manager setup ==="

              # Check if cert-manager is running
              echo "Checking cert-manager pods..."
              kubectl get pods -n cert-manager -l app.kubernetes.io/name=cert-manager

              # Check if ClusterIssuer is ready
              echo "Checking ClusterIssuer status..."
              kubectl get clusterissuer cloudflare-cluster-issuer -o wide

              # Check if the secret exists
              echo "Checking Cloudflare secret..."
              if kubectl get secret cloudflare-api-token -n cert-manager > /dev/null 2>&1; then
                echo "✓ Cloudflare API token secret exists"
              else
                echo "✗ Cloudflare API token secret missing!"
                echo "Create it with: kubectl create secret generic cloudflare-api-token --from-literal=api-token=YOUR_TOKEN -n cert-manager"
                exit 1
              fi

              # Check if ClusterIssuer is ready
              if kubectl get clusterissuer cloudflare-cluster-issuer -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' | grep -q "True"; then
                echo "✓ ClusterIssuer is ready"
              else
                echo "✗ ClusterIssuer is not ready"
                kubectl describe clusterissuer cloudflare-cluster-issuer
                exit 1
              fi

              echo "=== cert-manager validation completed successfully ==="
      serviceAccountName: cert-manager-validation
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cert-manager-validation
  namespace: cert-manager
  annotations:
    argocd.argoproj.io/sync-wave: "3"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cert-manager-validation
  annotations:
    argocd.argoproj.io/sync-wave: "3"
rules:
  - apiGroups: ["cert-manager.io"]
    resources: ["clusterissuers"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["secrets", "pods"]
    verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cert-manager-validation
  annotations:
    argocd.argoproj.io/sync-wave: "3"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cert-manager-validation
subjects:
  - kind: ServiceAccount
    name: cert-manager-validation
    namespace: cert-manager
