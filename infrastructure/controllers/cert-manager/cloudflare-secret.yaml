apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-api-token
  namespace: cert-manager
type: Opaque
# The actual secret data should be created using kubectl with real values:
# kubectl create secret generic cloudflare-api-token \
#   --namespace cert-manager \
#   --from-literal=api-token='your-api-token-here' \
#   --from-literal=email='your-cloudflare-email-here' \
#   --dry-run=client -o yaml | kubectl apply -f - 