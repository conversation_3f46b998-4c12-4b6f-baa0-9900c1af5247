apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ns.yaml
  # - cloudflare-secret.yaml
  - cluster-issuer.yaml
  - validate-setup.yaml
helmCharts:
  - name: cert-manager
    repo: https://charts.jetstack.io
    version: v1.18.2 # renovate: github-releases=cert-manager/cert-manager
    releaseName: cert-manager
    namespace: cert-manager
    valuesFile: values.yaml
commonAnnotations:
  argocd.argoproj.io/sync-wave: "-10"
