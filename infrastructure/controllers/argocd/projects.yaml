apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: infrastructure
  namespace: argocd
spec:
  description: Core infrastructure components
  sourceRepos:
    - https://github.com/mitchross/k3s-argocd-starter.git
  destinations:
    - namespace: "*"
      server: "*"
  clusterResourceWhitelist:
    - group: "*"
      kind: "*"
  namespaceResourceWhitelist:
    - group: "*"
      kind: "*"
  roles:
    - name: admin
      description: Admin role for infrastructure project
      policies:
        - p, proj:infrastructure:admin, applications, *, infrastructure/*, allow
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: applications
  namespace: argocd
spec:
  description: User applications
  sourceRepos:
    - https://github.com/mitchross/k3s-argocd-starter.git
  destinations:
    - namespace: "*"
      server: "*"
  clusterResourceWhitelist:
    - group: "*"
      kind: "*"
  namespaceResourceWhitelist:
    - group: "*"
      kind: "*"
  roles:
    - name: admin
      description: Admin role for applications project
      policies:
        - p, proj:applications:admin, applications, *, applications/*, allow
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: monitoring
  namespace: argocd
spec:
  description: Monitoring and observability components
  sourceRepos:
    - https://github.com/mitchross/k3s-argocd-starter.git
  destinations:
    - namespace: "*"
      server: "*"
  clusterResourceWhitelist:
    - group: "*"
      kind: "*"
  namespaceResourceWhitelist:
    - group: "*"
      kind: "*"
  roles:
    - name: admin
      description: Admin role for monitoring project
      policies:
        - p, proj:monitoring:admin, applications, *, monitoring/*, allow