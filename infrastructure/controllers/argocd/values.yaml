configs:
  cm:
    create: true
    application.resourceTrackingMethod: "annotation+label"
  cmp:
    create: true
    plugins:
      kustomize-build-with-helm:
        generate:
          command: [ "sh", "-c" ]
          args: [ "kustomize build --enable-helm" ]
  params:
    server.insecure: true

crds:
  install: true
  # -- Keep CRDs on chart uninstall
  keep: false

controller:
  resources:
    requests:
      cpu: 100m
      memory: 700Mi
    limits:
      cpu: 4000m
      memory: 2Gi

dex:
  resources:
    requests:
      cpu: 20m
      memory: 64Mi
    limits:
      cpu: 1000m
      memory: 256Mi

redis:
  resources:
    requests:
      cpu: 100m
      memory: 64Mi
    limits:
      cpu: 2000m
      memory: 128Mi

server:
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 4000m
      memory: 256Mi

repoServer:
  containerSecurityContext:
    readOnlyRootFilesystem: true
  volumes:
    - name: cmp-kustomize-build-with-helm
      configMap:
        name: argocd-cmp-cm
    - name: cmp-tmp
      emptyDir: {}
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 4000m
      memory: 1024Mi
  extraContainers:
    - name: kustomize-build-with-helm
      command:
        - argocd-cmp-server
      image: '{{ default .Values.global.image.repository .Values.repoServer.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.repoServer.image.tag }}'
      securityContext:
        runAsNonRoot: true
        runAsUser: 999
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
        seccompProfile:
          type: RuntimeDefault
        capabilities:
          drop: [ "ALL" ]
      volumeMounts:
        - name: plugins
          mountPath: /home/<USER>/cmp-server/plugins
        - name: cmp-kustomize-build-with-helm
          mountPath: /home/<USER>/cmp-server/config/plugin.yaml
          subPath: kustomize-build-with-helm.yaml
        - mountPath: /tmp
          name: cmp-tmp

applicationSet:
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 1000m
      memory: 512Mi

notifications:
  enabled: false
  resources:
    requests:
      cpu: 100m
      memory: 64Mi
    limits:
      cpu: 1000m
      memory: 128Mi
