---
# Example class for SMB storage
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: yourapp-smb
provisioner: smb.csi.k8s.io
parameters:
  # Point directly to the ollama subfolder on the SMB share
  source: //192.168.10.133/k8s/yourapp
  csi.storage.k8s.io/node-stage-secret-name: smbcreds
  csi.storage.k8s.io/node-stage-secret-namespace: csi-driver-smb
mountOptions:
    - dir_mode=0770
    - file_mode=0660
    - vers=3.0
    - noserverino # Recommended for stability
reclaimPolicy: Retain
volumeBindingMode: Immediate
allowVolumeExpansion: true
