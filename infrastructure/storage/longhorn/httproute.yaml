apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: longhorn
  namespace: longhorn-system
spec:
  parentRefs:
    - name: gateway-internal # Assuming you have an internal gateway, adjust if needed
      namespace: gateway # Assuming your gateway is in the 'gateway' namespace
  hostnames:
    - "longhorn.mvt.com.ar" # You can change this hostname
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: longhorn-frontend # Default Longhorn UI service name
          namespace: longhorn-system
          port: 80 # Default Longhorn UI port
          weight: 1
