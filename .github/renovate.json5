{
  $schema: 'https://docs.renovatebot.com/renovate-schema.json',
  extends: [
    'config:recommended',
  ],
  prHourlyLimit: 3,
  enabledManagers: [
    'helm-values',
    'kubernetes',
    'kustomize',
    'argocd',
  ],
  argocd: {
    managerFilePatterns: [
      '/^infra/.+\\.yaml$/',
      '/^apps/.+\\.yaml$/',
    ],
  },
  kubernetes: {
    managerFilePatterns: [
      '/^infra/.+\\.yaml$/',
      '/^apps/.+\\.yaml$/',
    ],
  },
  'helm-values': {
    managerFilePatterns: [
      '/^infra/.+/values\\.yaml$/',
      '/^apps/.+/values\\.yaml$/',
    ],
  },
  kustomize: {
    managerFilePatterns: [
      '/(^|/)kustomization\\.ya?ml$/',
    ],
  },
  packageRules: [
    {
      matchUpdateTypes: [
        'minor',
        'patch',
      ],
      matchManagers: [
        'helm-values',
        'kubernetes',
        'kustomize',
        'argocd',
      ],
      automerge: true,
      automergeType: 'branch',
    },
    {
      matchUpdateTypes: [
        'major',
      ],
      matchManagers: [
        'helm-values',
        'kubernetes',
        'kustomize',
        'argocd',
      ],
      automerge: false,
      prCreation: 'immediate',
    },
  ],
  ignorePaths: [
    '**/charts/**',
  ],
}
